#!/bin/bash

# vLLM 服务器启动脚本 (旧版本)
# 使用 python -m vllm.entrypoints.api_server 启动服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 创建日志目录
LOG_DIR="validation/logs/server"
mkdir -p "$LOG_DIR"

# 生成带时间戳的日志文件
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$LOG_DIR/vllm_server_old_$TIMESTAMP.log"

# 日志函数
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# 打印函数（同时输出到控制台和日志文件）
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
    log_message "INFO" "$1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    log_message "WARNING" "$1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    log_message "ERROR" "$1"
}

# 记录脚本开始
log_message "INFO" "vLLM 服务器启动脚本开始执行 (旧版本)"
log_message "INFO" "日志文件: $LOG_FILE"
log_message "INFO" "Python 路径: $PYTHON_PATH"

# 加载通用YAML解析脚本
if [ ! -f "validation/parse_yaml.sh" ]; then
    print_error "YAML解析脚本不存在: validation/parse_yaml.sh"
    exit 1
fi

source validation/parse_yaml.sh

# 包装parse_yaml函数以添加日志记录
parse_yaml_with_logging() {
    local file=$1
    local prefix=$2
    log_message "DEBUG" "解析配置文件: $file (前缀: $prefix)"

    if parse_yaml "$file" "$prefix"; then
        log_message "DEBUG" "配置文件解析成功: $file"
        return 0
    else
        log_message "ERROR" "配置文件解析失败: $file"
        return 1
    fi
}

# 检查配置文件
SHARED_CONFIG="validation/configs/config_shared.yaml"
VLLM_CONFIG="validation/configs/config_vllm.yaml"

print_info "检查配置文件..."

if [ ! -f "$SHARED_CONFIG" ]; then
    print_error "共享配置文件未找到: $SHARED_CONFIG"
    exit 1
fi

if [ ! -f "$VLLM_CONFIG" ]; then
    print_error "vLLM 配置文件未找到: $VLLM_CONFIG"
    exit 1
fi

print_info "配置文件检查通过"

# 解析配置文件
print_info "解析配置文件..."
parse_yaml_with_logging "$SHARED_CONFIG" "SHARED"
parse_yaml_with_logging "$VLLM_CONFIG" "VLLM"

# 检查模型路径
print_info "检查模型路径: $VLLM_model"
if [ ! -d "$VLLM_model" ]; then
    print_error "模型路径未找到: $VLLM_model"
    print_error "请确保模型已下载到指定路径。"
    exit 1
fi
print_info "模型路径检查通过"

print_info "开始启动 vLLM 服务器 (旧版本)..."
print_info "配置文件:"
print_info "  - 共享配置: $SHARED_CONFIG"
print_info "  - vLLM 配置: $VLLM_CONFIG"
print_info "  - 模型路径: $VLLM_model"

# 构建旧版本 API 服务器命令参数（使用下划线格式）
VLLM_ARGS=""

# 基础参数
VLLM_ARGS="$VLLM_ARGS --host ${VLLM_host:-localhost}"
VLLM_ARGS="$VLLM_ARGS --port ${VLLM_port:-8000}"

log_message "INFO" "基础参数配置完成: host=${VLLM_host:-localhost}, port=${VLLM_port:-8000}"

# 共享配置参数（转换为下划线格式）
if [ ! -z "$SHARED_max_model_len" ]; then
    VLLM_ARGS="$VLLM_ARGS --max_model_len $SHARED_max_model_len"
    log_message "INFO" "设置 max_model_len: $SHARED_max_model_len"
fi
if [ ! -z "$SHARED_dtype" ]; then
    VLLM_ARGS="$VLLM_ARGS --dtype $SHARED_dtype"
    log_message "INFO" "设置 dtype: $SHARED_dtype"
fi
if [ ! -z "$SHARED_tensor_parallel_size" ]; then
    VLLM_ARGS="$VLLM_ARGS --tensor_parallel_size $SHARED_tensor_parallel_size"
    log_message "INFO" "设置 tensor_parallel_size: $SHARED_tensor_parallel_size"
fi
if [ ! -z "$SHARED_pipeline_parallel_size" ]; then
    VLLM_ARGS="$VLLM_ARGS --pipeline_parallel_size $SHARED_pipeline_parallel_size"
    log_message "INFO" "设置 pipeline_parallel_size: $SHARED_pipeline_parallel_size"
fi
if [ ! -z "$SHARED_gpu_memory_utilization" ]; then
    VLLM_ARGS="$VLLM_ARGS --gpu_memory_utilization $SHARED_gpu_memory_utilization"
    log_message "INFO" "设置 gpu_memory_utilization: $SHARED_gpu_memory_utilization"
fi
if [ ! -z "$SHARED_block_size" ]; then
    VLLM_ARGS="$VLLM_ARGS --block_size $SHARED_block_size"
    log_message "INFO" "设置 block_size: $SHARED_block_size"
fi
if [ ! -z "$SHARED_max_num_seqs" ]; then
    VLLM_ARGS="$VLLM_ARGS --max_num_seqs $SHARED_max_num_seqs"
    log_message "INFO" "设置 max_num_seqs: $SHARED_max_num_seqs"
fi
if [ ! -z "$SHARED_max_num_batched_tokens" ]; then
    VLLM_ARGS="$VLLM_ARGS --max_num_batched_tokens $SHARED_max_num_batched_tokens"
    log_message "INFO" "设置 max_num_batched_tokens: $SHARED_max_num_batched_tokens"
fi
if [ ! -z "$SHARED_seed" ]; then
    VLLM_ARGS="$VLLM_ARGS --seed $SHARED_seed"
    log_message "INFO" "设置 seed: $SHARED_seed"
fi

# vLLM 特有配置参数（转换为下划线格式）
if [ "$SHARED_trust_remote_code" = "true" ]; then
    VLLM_ARGS="$VLLM_ARGS --trust_remote_code"
    log_message "INFO" "启用 trust_remote_code"
fi
if [ "$VLLM_disable_log_requests" = "true" ]; then
    VLLM_ARGS="$VLLM_ARGS --disable_log_requests"
    log_message "INFO" "禁用请求日志"
else
    log_message "INFO" "启用请求日志 - 将显示每个请求详情"
fi
if [ "$VLLM_disable_log_stats" = "true" ]; then
    VLLM_ARGS="$VLLM_ARGS --disable_log_stats"
    log_message "INFO" "禁用统计信息日志"
else
    log_message "INFO" "启用统计信息日志 - 将显示吞吐量和性能数据"
fi
if [ ! -z "$VLLM_tokenizer_mode" ]; then
    VLLM_ARGS="$VLLM_ARGS --tokenizer_mode $VLLM_tokenizer_mode"
    log_message "INFO" "设置 tokenizer_mode: $VLLM_tokenizer_mode"
fi
if [ ! -z "$VLLM_quantization" ]; then
    VLLM_ARGS="$VLLM_ARGS --quantization $VLLM_quantization"
    log_message "INFO" "设置 quantization: $VLLM_quantization"
fi
if [ "$VLLM_enforce_eager" = "true" ]; then
    VLLM_ARGS="$VLLM_ARGS --enforce_eager"
    log_message "INFO" "启用 enforce_eager"
fi
if [ ! -z "$VLLM_max_seq_len_to_capture" ]; then
    VLLM_ARGS="$VLLM_ARGS --max_seq_len_to_capture $VLLM_max_seq_len_to_capture"
    log_message "INFO" "设置 max_seq_len_to_capture: $VLLM_max_seq_len_to_capture"
fi
if [ ! -z "$VLLM_load_format" ]; then
    VLLM_ARGS="$VLLM_ARGS --load_format $VLLM_load_format"
    log_message "INFO" "设置 load_format: $VLLM_load_format"
fi
if [ ! -z "$VLLM_swap_space" ]; then
    VLLM_ARGS="$VLLM_ARGS --swap_space $VLLM_swap_space"
    log_message "INFO" "设置 swap_space: $VLLM_swap_space"
fi

# 旧版本的 prefix caching 处理
if [ "$VLLM_enable_prefix_caching" = "true" ]; then
    VLLM_ARGS="$VLLM_ARGS --enable_prefix_caching"
    log_message "INFO" "启用 prefix_caching"
else
    VLLM_ARGS="$VLLM_ARGS --disable_prefix_caching"
    log_message "INFO" "禁用 prefix_caching"
fi

# Attention Backend 配置
if [ ! -z "$VLLM_attention_backend" ]; then
    export VLLM_ATTENTION_BACKEND="$VLLM_attention_backend"
    log_message "INFO" "设置 attention backend: $VLLM_attention_backend"
fi

# 旧版本不设置 VLLM_USE_V1 环境变量
log_message "INFO" "旧版本 vLLM，不设置 VLLM_USE_V1 环境变量"

FULL_COMMAND="$PYTHON_PATH -m vllm.entrypoints.api_server --model $VLLM_model $VLLM_ARGS"
print_info "启动命令: $FULL_COMMAND"
log_message "INFO" "完整启动命令: $FULL_COMMAND"

# 设置错误处理
cleanup() {
    log_message "INFO" "接收到信号，正在清理..."
    exit 0
}

trap cleanup SIGINT SIGTERM

# 启动 vLLM 服务器并将输出重定向到日志文件
print_info "启动 vLLM 服务器 (旧版本)，输出将同时显示在控制台和保存到日志文件..."
log_message "INFO" "vLLM 服务器启动开始"

# 使用 tee 将 vLLM 的输出同时显示在控制台和保存到日志文件
exec python -m vllm.entrypoints.api_server --model $VLLM_model $VLLM_ARGS 2>&1 | tee -a "$LOG_FILE"
